<?xml version="1.0"?>
<doc>
    <assembly>
        <name>R3</name>
    </assembly>
    <members>
        <member name="F:R3.AwaitOperation.Sequential">
            <summary>All values are queued, and the next value waits for the completion of the asynchronous method.</summary>
        </member>
        <member name="F:R3.AwaitOperation.Drop">
            <summary>Drop new value when async operation is running.</summary>
        </member>
        <member name="F:R3.AwaitOperation.Switch">
            <summary>If the previous asynchronous method is running, it is cancelled and the next asynchronous method is executed.</summary>
        </member>
        <member name="F:R3.AwaitOperation.Parallel">
            <summary>All values are sent immediately to the asynchronous method.</summary>
        </member>
        <member name="F:R3.AwaitOperation.SequentialParallel">
            <summary>All values are sent immediately to the asynchronous method, but the results are queued and passed to the next operator in order.</summary>
        </member>
        <member name="F:R3.AwaitOperation.ThrottleFirstLast">
            <summary>Send the first value and the last value while the asynchronous method is running.</summary>
        </member>
        <member name="M:R3.ObservableExtensions.ObserveOnCurrentSynchronizationContext``1(R3.Observable{``0})">
            <summary>ObserveOn SynchronizationContext.Current</summary>
        </member>
        <member name="M:R3.ObservableExtensions.SelectAwait``2(R3.Observable{``0},System.Func{``0,System.Threading.CancellationToken,System.Threading.Tasks.ValueTask{``1}},R3.AwaitOperation,System.Boolean,System.Boolean,System.Int32)">
            <param name="maxConcurrent">This option is only valid for AwaitOperation.Parallel and AwaitOperation.SequentialParallel. It sets the number of concurrent executions. If set to -1, there is no limit.</param>
        </member>
        <member name="M:R3.ObservableExtensions.SubscribeAwait``1(R3.Observable{``0},System.Func{``0,System.Threading.CancellationToken,System.Threading.Tasks.ValueTask},R3.AwaitOperation,System.Boolean,System.Boolean,System.Int32)">
            <param name="maxConcurrent">This option is only valid for AwaitOperation.Parallel and AwaitOperation.SequentialParallel. It sets the number of concurrent executions. If set to -1, there is no limit.</param>
        </member>
        <member name="M:R3.ObservableExtensions.SubscribeAwait``1(R3.Observable{``0},System.Func{``0,System.Threading.CancellationToken,System.Threading.Tasks.ValueTask},System.Action{R3.Result},R3.AwaitOperation,System.Boolean,System.Boolean,System.Int32)">
            <param name="maxConcurrent">This option is only valid for AwaitOperation.Parallel and AwaitOperation.SequentialParallel. It sets the number of concurrent executions. If set to -1, there is no limit.</param>
        </member>
        <member name="M:R3.ObservableExtensions.SubscribeAwait``1(R3.Observable{``0},System.Func{``0,System.Threading.CancellationToken,System.Threading.Tasks.ValueTask},System.Action{System.Exception},System.Action{R3.Result},R3.AwaitOperation,System.Boolean,System.Boolean,System.Int32)">
            <param name="maxConcurrent">This option is only valid for AwaitOperation.Parallel and AwaitOperation.SequentialParallel. It sets the number of concurrent executions. If set to -1, there is no limit.</param>
        </member>
        <member name="M:R3.ObservableExtensions.SubscribeAwait``2(R3.Observable{``0},``1,System.Func{``0,``1,System.Threading.CancellationToken,System.Threading.Tasks.ValueTask},R3.AwaitOperation,System.Boolean,System.Boolean,System.Int32)">
            <param name="maxConcurrent">This option is only valid for AwaitOperation.Parallel and AwaitOperation.SequentialParallel. It sets the number of concurrent executions. If set to -1, there is no limit.</param>
        </member>
        <member name="M:R3.ObservableExtensions.SubscribeAwait``2(R3.Observable{``0},``1,System.Func{``0,``1,System.Threading.CancellationToken,System.Threading.Tasks.ValueTask},System.Action{R3.Result,``1},R3.AwaitOperation,System.Boolean,System.Boolean,System.Int32)">
            <param name="maxConcurrent">This option is only valid for AwaitOperation.Parallel and AwaitOperation.SequentialParallel. It sets the number of concurrent executions. If set to -1, there is no limit.</param>
        </member>
        <member name="M:R3.ObservableExtensions.Trampoline``1(R3.Observable{``0})">
            <summary>
            Similar as ObserveOn(CurrentThreadScheduler) in dotnet/reactive, place the execution order of recursive calls after the call is completed.
            </summary>
        </member>
        <member name="M:R3.ObservableExtensions.WhereAwait``1(R3.Observable{``0},System.Func{``0,System.Threading.CancellationToken,System.Threading.Tasks.ValueTask{System.Boolean}},R3.AwaitOperation,System.Boolean,System.Boolean,System.Int32)">
            <param name="maxConcurrent">This option is only valid for AwaitOperation.Parallel and AwaitOperation.SequentialParallel. It sets the number of concurrent executions. If set to -1, there is no limit.</param>
        </member>
        <member name="M:R3.Observable.ObservePropertyChanged``2(``0,System.Func{``0,``1},System.Boolean,System.Threading.CancellationToken,System.String)">
            <summary>
            Convert INotifyPropertyChanged to Observable.
            `propertySelector` must be a Func specifying a simple property. For example, it extracts "Foo" from `x => x.Foo`.
            </summary>
        </member>
        <member name="M:R3.Observable.ObservePropertyChanged``3(``0,System.Func{``0,``1},System.Func{``1,``2},System.Boolean,System.Threading.CancellationToken,System.String,System.String)">
            <summary>
            Convert INotifyPropertyChanged to Observable.
            `propertySelector1` and `propertySelector2` must be a Func specifying a simple property. For example, it extracts "Foo" from `x => x.Foo`.
            </summary>
        </member>
        <member name="M:R3.Observable.ObservePropertyChanged``4(``0,System.Func{``0,``1},System.Func{``1,``2},System.Func{``2,``3},System.Boolean,System.Threading.CancellationToken,System.String,System.String,System.String)">
            <summary>
            Convert INotifyPropertyChanged to Observable.
            `propertySelector1`, `propertySelector2`, and `propertySelector3` must be a Func specifying a simple property. For example, it extracts "Foo" from `x => x.Foo`.
            </summary>
        </member>
        <member name="M:R3.Observable.ObservePropertyChanging``2(``0,System.Func{``0,``1},System.Boolean,System.Threading.CancellationToken,System.String)">
            <summary>
            Convert INotifyPropertyChanging to Observable.
            `propertySelector` must be a Func specifying a simple property. For example, it extracts "Foo" from `x => x.Foo`.
            </summary>
        </member>
        <member name="M:R3.Observable.ObservePropertyChanging``3(``0,System.Func{``0,``1},System.Func{``1,``2},System.Boolean,System.Threading.CancellationToken,System.String,System.String)">
            <summary>
            Convert INotifyPropertyChanging to Observable.
            `propertySelector1` and `propertySelector2` must be a Func specifying a simple property. For example, it extracts "Foo" from `x => x.Foo`.
            </summary>
        </member>
        <member name="M:R3.Observable.ObservePropertyChanging``4(``0,System.Func{``0,``1},System.Func{``1,``2},System.Func{``2,``3},System.Boolean,System.Threading.CancellationToken,System.String,System.String,System.String)">
            <summary>
            Convert INotifyPropertyChanging to Observable.
            `propertySelector1`, `propertySelector2`, and `propertySelector3` must be a Func specifying a simple property. For example, it extracts "Foo" from `x => x.Foo`.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute">
            <summary>
            An attribute that allows parameters to receive the expression of other parameters.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute"/> class.
            </summary>
            <param name="parameterName">The condition parameter value.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute.ParameterName">
            <summary>
            Gets the parameter name the expression is retrieved from.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CollectionBuilderAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initialize the attribute to refer to the <paramref name="methodName"/> method on the <paramref name="builderType"/> type.
            </summary>
            <param name="builderType">The type of the builder to use to construct the collection.</param>
            <param name="methodName">The name of the method on the builder to use to construct the collection.</param>
            <remarks>
            <paramref name="methodName"/> must refer to a static method that accepts a single parameter of
            type <see cref="T:System.ReadOnlySpan`1"/> and returns an instance of the collection being built containing
            a copy of the data from that span.  In future releases of .NET, additional patterns may be supported.
            </remarks>
        </member>
        <member name="P:System.Runtime.CompilerServices.CollectionBuilderAttribute.BuilderType">
            <summary>
            Gets the type of the builder to use to construct the collection.
            </summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.CollectionBuilderAttribute.MethodName">
            <summary>
            Gets the name of the method on the builder to use to construct the collection.
            </summary>
            <remarks>
            This should match the metadata name of the target method.
            For example, this might be ".ctor" if targeting the type's constructor.
            </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute">
            <summary>
            Indicates that compiler support for a particular feature is required for the location where this attribute is applied.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.#ctor(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute"/> type.
            </summary>
            <param name="featureName">The name of the feature to indicate.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName">
            <summary>
            The name of the compiler feature.
            </summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.IsOptional">
            <summary>
            If true, the compiler can choose to allow access to the location where this attribute is applied if it does not understand <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.RefStructs">
            <summary>
            The <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/> used for the ref structs C# feature.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.RequiredMembers">
            <summary>
            The <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/> used for the required members C# feature.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute">
            <summary>
            Indicates which arguments to a method involving an interpolated string handler should be passed to that handler.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute"/> class.
            </summary>
            <param name="argument">The name of the argument that should be passed to the handler.</param>
            <remarks><see langword="null"/> may be used as the name of the receiver in an instance method.</remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute.#ctor(System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute"/> class.
            </summary>
            <param name="arguments">The names of the arguments that should be passed to the handler.</param>
            <remarks><see langword="null"/> may be used as the name of the receiver in an instance method.</remarks>
        </member>
        <member name="P:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute.Arguments">
            <summary>
            Gets the names of the arguments that should be passed to the handler.
            </summary>
            <remarks><see langword="null"/> may be used as the name of the receiver in an instance method.</remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.InterpolatedStringHandlerAttribute">
            <summary>
            Indicates the attributed type is to be used as an interpolated string handler.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.IsExternalInit">
            <summary>
            Reserved to be used by the compiler for tracking metadata.
            This class should not be used by developers in source code.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.ModuleInitializerAttribute">
             <summary>
             Used to indicate to the compiler that a method should be called
             in its containing module's initializer.
             </summary>
             <remarks>
             When one or more valid methods
             with this attribute are found in a compilation, the compiler will
             emit a module initializer which calls each of the attributed methods.
            
             Certain requirements are imposed on any method targeted with this attribute:
             - The method must be `static`.
             - The method must be an ordinary member method, as opposed to a property accessor, constructor, local function, etc.
             - The method must be parameterless.
             - The method must return `void`.
             - The method must not be generic or be contained in a generic type.
             - The method's effective accessibility must be `internal` or `public`.
            
             The specification for module initializers in the .NET runtime can be found here:
             https://github.com/dotnet/runtime/blob/main/docs/design/specs/Ecma-335-Augments.md#module-initializer
             </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.RequiredMemberAttribute">
            <summary>
            Specifies that a type has required members or that a member is required.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.RequiresLocationAttribute">
            <summary>
            Reserved for use by a compiler for tracking metadata.
            This attribute should not be used by developers in source code.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.SkipLocalsInitAttribute">
            <summary>
            Used to indicate to the compiler that the <c>.locals init</c> flag should not be set in method headers.
            </summary>
        </member>
        <member name="M:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute"/> class.
            </summary>
        </member>
        <member name="M:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute"/> class with the specified message.
            </summary>
            <param name="message">An optional message associated with this attribute instance.</param>
        </member>
        <member name="P:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.Message">
            <summary>
            Returns the optional message associated with this attribute instance.
            </summary>
        </member>
        <member name="P:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.Url">
            <summary>
            Returns the optional URL associated with this attribute instance.
            </summary>
        </member>
        <member name="T:System.Diagnostics.StackTraceHiddenAttribute">
            <summary>
            Types and Methods attributed with StackTraceHidden will be omitted from the stack trace text shown in StackTrace.ToString()
            and Exception.StackTrace
            </summary>
        </member>
        <member name="M:System.Diagnostics.StackTraceHiddenAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.StackTraceHiddenAttribute"/> class.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.ExperimentalAttribute">
            <summary>
            Indicates that an API is experimental and it may change in the future.
            </summary>
            <remarks>
            This attribute allows call sites to be flagged with a diagnostic that indicates that an experimental
            feature is used. Authors can use this attribute to ship preview features in their assemblies.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.ExperimentalAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.ExperimentalAttribute"/> class,
            specifying the ID that the compiler will use when reporting a use of the API the attribute applies to.
            </summary>
            <param name="diagnosticId">The ID that the compiler will use when reporting a use of the API the attribute applies to.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ExperimentalAttribute.DiagnosticId">
            <summary>
            Gets the ID that the compiler will use when reporting a use of the API the attribute applies to.
            </summary>
            <value>The unique diagnostic ID.</value>
            <remarks>
            The diagnostic ID is shown in build output for warnings and errors.
            <para>This property represents the unique ID that can be used to suppress the warnings or errors, if needed.</para>
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ExperimentalAttribute.UrlFormat">
            <summary>
            Gets or sets the URL for corresponding documentation.
            The API accepts a format string instead of an actual URL, creating a generic URL that includes the diagnostic ID.
            </summary>
            <value>The format string that represents a URL to corresponding documentation.</value>
            <remarks>An example format string is <c>https://contoso.com/obsoletion-warnings/{0}</c>.</remarks>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>
            Specifies that the method or property will ensure that the listed field and property members have not-null values.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>
            Initializes the attribute with a field or property member.
            </summary>
            <param name="member">The field or property member that is promised to be not-null.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>
            Initializes the attribute with the list of field and property members.
            </summary>
            <param name="members">The list of field and property members that are promised to be not-null.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>
            Gets field or property member names.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>
            Specifies that the method or property will ensure that the listed field and property
            members have not-null values when returning with the specified return value condition.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>
            Initializes the attribute with the specified return value condition and a field or property member.
            </summary>
            <param name="returnValue">The return value condition. If the method returns this value, the associated parameter will not be null.</param>
            <param name="member">The field or property member that is promised to be not-null.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>
            Initializes the attribute with the specified return value condition and list of field and property members.
            </summary>
            <param name="returnValue">The return value condition. If the method returns this value, the associated parameter will not be null.</param>
            <param name="members">The list of field and property members that are promised to be not-null.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>
            Gets the return value condition.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>
            Gets field or property member names.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.SetsRequiredMembersAttribute">
            <summary>
            Specifies that this constructor sets all required members for the current type,
            and callers do not need to set any required members themselves.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute">
            <summary>
            Specifies the syntax used in a string.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.#ctor(System.String)">
            <summary>
            Initializes the <see cref="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute"/> with the identifier of the syntax used.
            </summary>
            <param name="syntax">The syntax identifier.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.#ctor(System.String,System.Object[])">
            <summary>Initializes the <see cref="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute"/> with the identifier of the syntax used.</summary>
            <param name="syntax">The syntax identifier.</param>
            <param name="arguments">Optional arguments associated with the specific syntax employed.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Syntax">
            <summary>Gets the identifier of the syntax used.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Arguments">
            <summary>Optional arguments associated with the specific syntax employed.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.CompositeFormat">
            <summary>The syntax identifier for strings containing composite formats for string formatting.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.DateOnlyFormat">
            <summary>The syntax identifier for strings containing date format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.DateTimeFormat">
            <summary>The syntax identifier for strings containing date and time format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.EnumFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.Enum"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.GuidFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.Guid"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Json">
            <summary>The syntax identifier for strings containing JavaScript Object Notation (JSON).</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.NumericFormat">
            <summary>The syntax identifier for strings containing numeric format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Regex">
            <summary>The syntax identifier for strings containing regular expressions.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.TimeOnlyFormat">
            <summary>The syntax identifier for strings containing time format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.TimeSpanFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.TimeSpan"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Uri">
            <summary>The syntax identifier for strings containing URIs.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Xml">
            <summary>The syntax identifier for strings containing XML.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.UnscopedRefAttribute">
            <summary>
            Used to indicate a byref escapes and is not scoped.
            </summary>
            <remarks>
            <para>
            There are several cases where the C# compiler treats a <see langword="ref"/> as implicitly
            <see langword="scoped"/> - where the compiler does not allow the <see langword="ref"/> to escape the method.
            </para>
            <para>
            For example:
            <list type="number">
                <item><see langword="this"/> for <see langword="struct"/> instance methods.</item>
                <item><see langword="ref"/> parameters that refer to <see langword="ref"/> <see langword="struct"/> types.</item>
                <item><see langword="out"/> parameters.</item>
            </list>
            </para>
            <para>
            This attribute is used in those instances where the <see langword="ref"/> should be allowed to escape.
            </para>
            <para>
            Applying this attribute, in any form, has impact on consumers of the applicable API. It is necessary for
            API authors to understand the lifetime implications of applying this attribute and how it may impact their users.
            </para>
            </remarks>
        </member>
    </members>
</doc>
