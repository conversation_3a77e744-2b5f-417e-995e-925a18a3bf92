﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>R3</id>
    <version>1.3.0</version>
    <authors>Cysharp</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>Icon.png</icon>
    <projectUrl>https://github.com/Cysharp/R3</projectUrl>
    <description>The evolution of dotnet/reactive and UniRx.</description>
    <copyright>© Cysharp, Inc.</copyright>
    <tags>rx</tags>
    <repository type="git" url="https://github.com/Cysharp/R3" commit="329ca7915713417f2e0837b0e0a80b4da074db4a" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Microsoft.Bcl.TimeProvider" version="8.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net8.0" />
      <group targetFramework=".NETStandard2.0">
        <dependency id="Microsoft.Bcl.TimeProvider" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Buffers" version="4.5.1" exclude="Build,Analyzers" />
        <dependency id="System.ComponentModel.Annotations" version="5.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Memory" version="4.5.5" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Channels" version="8.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="Microsoft.Bcl.TimeProvider" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.ComponentModel.Annotations" version="5.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Channels" version="8.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>