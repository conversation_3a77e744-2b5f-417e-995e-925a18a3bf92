﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata minClientVersion="2.12">
    <id>System.ComponentModel.Annotations</id>
    <version>5.0.0</version>
    <title>System.ComponentModel.Annotations</title>
    <authors>Microsoft</authors>
    <owners>microsoft,dotnetframework</owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>Icon.png</icon>
    <projectUrl>https://github.com/dotnet/runtime</projectUrl>
    <iconUrl>http://go.microsoft.com/fwlink/?LinkID=288859</iconUrl>
    <description>Provides attributes that are used to define metadata for objects used as data sources.

Commonly Used Types:
System.ComponentModel.DataAnnotations.ValidationResult
System.ComponentModel.DataAnnotations.IValidatableObject
System.ComponentModel.DataAnnotations.ValidationAttribute
System.ComponentModel.DataAnnotations.RequiredAttribute
System.ComponentModel.DataAnnotations.StringLengthAttribute
System.ComponentModel.DataAnnotations.DisplayAttribute
System.ComponentModel.DataAnnotations.RegularExpressionAttribute
System.ComponentModel.DataAnnotations.DataTypeAttribute
System.ComponentModel.DataAnnotations.RangeAttribute
System.ComponentModel.DataAnnotations.KeyAttribute
 
When using NuGet 3.x this package requires at least version 3.4.</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <repository type="git" url="git://github.com/dotnet/runtime" commit="cf258a14b70ad9069470a108f13765e0e5988f51" />
    <dependencies>
      <group targetFramework="MonoAndroid1.0" />
      <group targetFramework="MonoTouch1.0" />
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETFramework4.6.1" />
      <group targetFramework=".NETCore5.0">
        <dependency id="NETStandard.Library" version="1.6.1" />
        <dependency id="System.ComponentModel" version="4.3.0" />
      </group>
      <group targetFramework=".NETStandard1.1">
        <dependency id="NETStandard.Library" version="1.6.1" />
      </group>
      <group targetFramework=".NETStandard1.3">
        <dependency id="NETStandard.Library" version="1.6.1" />
        <dependency id="System.ComponentModel" version="4.3.0" />
      </group>
      <group targetFramework=".NETStandard1.4">
        <dependency id="NETStandard.Library" version="1.6.1" />
        <dependency id="System.ComponentModel" version="4.3.0" />
      </group>
      <group targetFramework=".NETStandard2.0" />
      <group targetFramework=".NETStandard2.1" />
      <group targetFramework=".NETPortable0.0-Profile7" />
      <group targetFramework="UAP10.0.16299" />
      <group targetFramework="Windows8.0" />
      <group targetFramework="Xamarin.iOS1.0" />
      <group targetFramework="Xamarin.Mac2.0" />
      <group targetFramework="Xamarin.TVOS1.0" />
      <group targetFramework="Xamarin.WatchOS1.0" />
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="mscorlib" targetFramework=".NETFramework4.6.1" />
      <frameworkAssembly assemblyName="System.ComponentModel.DataAnnotations" targetFramework=".NETFramework4.5" />
      <frameworkAssembly assemblyName="System.ComponentModel.DataAnnotations" targetFramework=".NETFramework4.6.1" />
    </frameworkAssemblies>
  </metadata>
</package>