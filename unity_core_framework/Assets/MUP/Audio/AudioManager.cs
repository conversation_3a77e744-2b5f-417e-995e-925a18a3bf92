using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using MUP.Core;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;

namespace MUP.Audio
{
    public class AudioManager : Singleton<AudioManager>
    {
        private bool isMutedSfx;
        private bool isMutedBgm;
        private AudioSource bgmSource;
        private List<AudioSource> sfxSources;
        private AudioSO audioSO;

        public async UniTask Init()
        {
            isMutedSfx = PlayerPrefs.GetInt("isMutedSfx", 0) == 1;
            isMutedBgm = PlayerPrefs.GetInt("isMutedBgm", 0) == 1;


            bgmSource = gameObject.AddComponent(typeof(AudioSource)) as AudioSource;
            sfxSources = new List<AudioSource>();

            PoolManager.Instance.InitPool(EPoolType.SfxObject, 10, 10);

            AsyncOperationHandle<AudioSO> handle = Addressables.LoadAssetAsync<AudioSO>("Assets/MUP/SO/AudioSO.asset");
            audioSO = await handle.ToUniTask(cancellationToken: this.GetCancellationTokenOnDestroy());
        }

        public void MuteSfx(bool mute)
        {
            isMutedSfx = mute;
            PlayerPrefs.SetInt("isMutedSfx", mute ? 1 : 0);
        }

        public void MuteBgm(bool mute)
        {
            isMutedBgm = mute;
            PlayerPrefs.SetInt("isMutedBgm", mute ? 1 : 0);
        }

        public void PlaySfx(string sfxName)
        {
            if (isMutedSfx) return;
            GameObject go = PoolManager.Instance.Get(EPoolType.SfxObject);
            SfxObject sfxObject = go.GetComponent<SfxObject>();
            sfxSources.Add(sfxObject.audioSource);
            sfxObject.PlaySfx(audioSO.GetSfx(sfxName)).Forget();
        }

        public void StopSfx()
        {
            if (isMutedSfx) return;
            foreach (AudioSource sfxSource in sfxSources)
            {
                sfxSource.Stop();
            }
        }

        public void PauseSfx()
        {
            if (isMutedSfx) return;
            foreach (AudioSource sfxSource in sfxSources)
            {
                sfxSource.Pause();
            }
        }

        public void UnPauseSfx()
        {
            if (isMutedSfx) return;
            foreach (AudioSource sfxSource in sfxSources)
            {
                sfxSource.UnPause();
            }
        }

        public void PlayBgm(string bgmName, bool isLoop = true)
        {
            if (isMutedBgm) return;
            bgmSource.clip = audioSO.GetBgm(bgmName);
            bgmSource.loop = isLoop;
            bgmSource.Play();
        }

        public void StopBgm()
        {
            if (isMutedBgm) return;
            bgmSource.Stop();
        }

        public void PauseBgm()
        {
            if (isMutedBgm) return;
            bgmSource.Pause();
        }

        public void UnPauseBgm()
        {
            if (isMutedBgm) return;
            bgmSource.UnPause();
        }
    }
}