using System;
using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName = "PoolSO", menuName = "MUP/Create Pool SO")]
public class PoolSO : ScriptableObject
{
    [Serializable]
    public class PoolEntry
    {
        public string key;
        public GameObject prefab;

        public PoolEntry(string key, GameObject prefab)
        {
            this.key = key;
            this.prefab = prefab;
        }
    }

    [Header("Pool Objects")]
    [SerializeField] private List<PoolEntry> poolEntries = new List<PoolEntry>();

    private Dictionary<string, GameObject> poolDictionary;

    public Dictionary<string, GameObject> PoolDictionary
    {
        get
        {
            if (poolDictionary == null)
                InitPool();
            return poolDictionary;
        }
    }


    private void InitPool()
    {
        poolDictionary = new Dictionary<string, GameObject>();
        foreach (var entry in poolEntries)
        {
            if (!string.IsNullOrEmpty(entry.key) && entry.prefab != null)
            {
                if (!poolDictionary.ContainsKey(entry.key))
                {
                    poolDictionary.Add(entry.key, entry.prefab);
                }
                else
                {
                    Debug.LogWarning($"Duplicate Pool key found: {entry.key}");
                }
            }
        }
    }


    public GameObject GetPool(string key)
    {
        if (PoolDictionary.TryGetValue(key, out GameObject prefab))
        {
            return prefab;
        }

        Debug.LogWarning($"Pool with key '{key}' not found!");
        return null;
    }
}