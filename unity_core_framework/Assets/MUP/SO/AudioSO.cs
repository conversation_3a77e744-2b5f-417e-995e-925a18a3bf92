using System;
using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName = "AudioSO", menuName = "MUP/Create Audio SO")]
public class AudioSO : ScriptableObject
{
    [Serializable]
    public class AudioEntry
    {
        public string key;
        public AudioClip clip;

        public AudioEntry(string key, AudioClip clip)
        {
            this.key = key;
            this.clip = clip;
        }
    }

    [Header("Sound Effects")]
    [SerializeField] private List<AudioEntry> sfxEntries = new List<AudioEntry>();

    [Header("Background Music")]
    [SerializeField] private List<AudioEntry> bgmEntries = new List<AudioEntry>();

    private Dictionary<string, AudioClip> sfxDictionary;
    private Dictionary<string, AudioClip> bgmDictionary;

    public Dictionary<string, AudioClip> SfxDictionary
    {
        get
        {
            if (sfxDictionary == null)
                InitSfx();
            return sfxDictionary;
        }
    }

    public Dictionary<string, AudioClip> BgmDictionary
    {
        get
        {
            if (bgmDictionary == null)
                InitBgm();
            return bgmDictionary;
        }
    }

    private void InitSfx()
    {
        sfxDictionary = new Dictionary<string, AudioClip>();
        foreach (var entry in sfxEntries)
        {
            if (!string.IsNullOrEmpty(entry.key) && entry.clip != null)
            {
                if (!sfxDictionary.ContainsKey(entry.key))
                {
                    sfxDictionary.Add(entry.key, entry.clip);
                }
                else
                {
                    Debug.LogWarning($"Duplicate SFX key found: {entry.key}");
                }
            }
        }
    }
    private void InitBgm()
    {
        bgmDictionary = new Dictionary<string, AudioClip>();
        foreach (var entry in bgmEntries)
        {
            if (!string.IsNullOrEmpty(entry.key) && entry.clip != null)
            {
                if (!bgmDictionary.ContainsKey(entry.key))
                {
                    bgmDictionary.Add(entry.key, entry.clip);
                }
                else
                {
                    Debug.LogWarning($"Duplicate BGM key found: {entry.key}");
                }
            }
        }
    }

    public AudioClip GetSfx(string key)
    {
        if (SfxDictionary.TryGetValue(key, out AudioClip clip))
        {
            return clip;
        }

        Debug.LogWarning($"SFX with key '{key}' not found!");
        return null;
    }

    public AudioClip GetBgm(string key)
    {
        if (BgmDictionary.TryGetValue(key, out AudioClip clip))
        {
            return clip;
        }

        Debug.LogWarning($"BGM with key '{key}' not found!");
        return null;
    }
}