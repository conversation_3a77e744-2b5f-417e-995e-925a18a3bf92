using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace MUP.Core
{
    public class Booster
    {
        private readonly List<Func<UniTask>> _requiredTasks = new();
        private readonly List<Func<UniTask>> _dependentTasks = new();
        private readonly List<Func<UniTask>> _independentTasks = new();
        private readonly List<ChainedTaskGroup> _chainedTaskGroups = new();

        public Booster AddRequiredTask(Func<UniTask> task)
        {
            _requiredTasks.Add(task);
            return this;
        }

        /// <summary>
        /// Thêm task phụ thuộc - chỉ chạy sau khi tất cả required tasks hoàn thành
        /// </summary>
        public Booster AddDependentTask(Func<UniTask> task)
        {
            _dependentTasks.Add(task);
            return this;
        }

        /// <summary>
        /// Thêm task độc lập - chạy song song không cần chờ
        /// </summary>
        public Booster AddIndependentTask(Func<UniTask> task)
        {
            _independentTasks.Add(task);
            return this;
        }

        /// <summary>
        /// Tạo một chain tasks mới - các task sẽ chạy tuần tự theo thứ tự
        /// </summary>
        public ChainBuilder CreateChain()
        {
            return new ChainBuilder(this);
        }

        /// <summary>
        /// Chạy tất cả tasks theo thứ tự ưu tiên
        /// </summary>
        public async UniTask ExecuteAsync()
        {
            try
            {
                // 1. Chạy tất cả independent tasks ngay lập tức (không chờ)
                var independentTasksList = new List<UniTask>();
                foreach (var task in _independentTasks)
                {
                    independentTasksList.Add(task().SuppressCancellationThrow());
                }

                // 2. Chạy tất cả required tasks và chờ hoàn thành
                if (_requiredTasks.Count > 0)
                {
                    var requiredTasksList = new List<UniTask>();
                    foreach (var task in _requiredTasks)
                    {
                        requiredTasksList.Add(task());
                    }
                    await UniTask.WhenAll(requiredTasksList);
                }

                // 3. Chạy dependent tasks và chained tasks song song
                var allDependentTasks = new List<UniTask>();

                // Thêm dependent tasks thông thường
                foreach (var task in _dependentTasks)
                {
                    allDependentTasks.Add(task());
                }

                // Thêm chained task groups
                foreach (var chainGroup in _chainedTaskGroups)
                {
                    allDependentTasks.Add(ExecuteChainAsync(chainGroup));
                }

                if (allDependentTasks.Count > 0)
                {
                    await UniTask.WhenAll(allDependentTasks);
                }

                // 4. Chờ independent tasks hoàn thành (optional)
                if (independentTasksList.Count > 0)
                {
                    await UniTask.WhenAll(independentTasksList);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Booster execution failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Chạy một chain tasks tuần tự
        /// </summary>
        private async UniTask ExecuteChainAsync(ChainedTaskGroup chainGroup)
        {
            try
            {
                foreach (var task in chainGroup.Tasks)
                {
                    await task();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Chain '{chainGroup.Name}' execution failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Chạy và quên independent tasks (fire-and-forget)
        /// </summary>
        public async UniTask ExecuteWithFireAndForgetAsync()
        {
            try
            {
                // 1. Fire-and-forget independent tasks
                foreach (var task in _independentTasks)
                {
                    task().Forget();
                }

                // 2. Chạy required tasks
                if (_requiredTasks.Count > 0)
                {
                    var requiredTasksList = new List<UniTask>();
                    foreach (var task in _requiredTasks)
                    {
                        requiredTasksList.Add(task());
                    }
                    await UniTask.WhenAll(requiredTasksList);
                }

                // 3. Chạy dependent tasks và chained tasks
                var allDependentTasks = new List<UniTask>();

                foreach (var task in _dependentTasks)
                {
                    allDependentTasks.Add(task());
                }

                foreach (var chainGroup in _chainedTaskGroups)
                {
                    allDependentTasks.Add(ExecuteChainAsync(chainGroup));
                }

                if (allDependentTasks.Count > 0)
                {
                    await UniTask.WhenAll(allDependentTasks);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Booster execution failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Clear tất cả tasks
        /// </summary>
        public void Clear()
        {
            _requiredTasks.Clear();
            _dependentTasks.Clear();
            _independentTasks.Clear();
            _chainedTaskGroups.Clear();
        }

        /// <summary>
        /// Kiểm tra có tasks nào không
        /// </summary>
        public bool HasTasks => _requiredTasks.Count > 0 || _dependentTasks.Count > 0 ||
                               _independentTasks.Count > 0 || _chainedTaskGroups.Count > 0;

        // Internal method để thêm chain group
        internal void AddChainedTaskGroup(ChainedTaskGroup chainGroup)
        {
            _chainedTaskGroups.Add(chainGroup);
        }
    }

    /// <summary>
    /// Nhóm các task chạy tuần tự theo chain
    /// </summary>
    public class ChainedTaskGroup
    {
        public string Name { get; }
        public List<Func<UniTask>> Tasks { get; } = new();

        public ChainedTaskGroup(string name = "")
        {
            Name = string.IsNullOrEmpty(name) ? $"Chain_{Guid.NewGuid().ToString("N")[..8]}" : name;
        }
    }

    /// <summary>
    /// Builder pattern để tạo chain tasks một cách dễ dàng
    /// </summary>
    public class ChainBuilder
    {
        private readonly Booster _booster;
        private readonly ChainedTaskGroup _chainGroup;

        internal ChainBuilder(Booster booster, string chainName = "")
        {
            _booster = booster;
            _chainGroup = new ChainedTaskGroup(chainName);
        }

        /// <summary>
        /// Thêm task vào chain
        /// </summary>
        public ChainBuilder Then(Func<UniTask> task)
        {
            _chainGroup.Tasks.Add(task);
            return this;
        }

        /// <summary>
        /// Kết thúc chain và thêm vào booster
        /// </summary>
        public Booster Build()
        {
            if (_chainGroup.Tasks.Count > 0)
            {
                _booster.AddChainedTaskGroup(_chainGroup);
            }
            return _booster;
        }

        /// <summary>
        /// Đặt tên cho chain (optional)
        /// </summary>
        public static ChainBuilder Create(Booster booster, string chainName)
        {
            return new ChainBuilder(booster, chainName);
        }
    }
}

