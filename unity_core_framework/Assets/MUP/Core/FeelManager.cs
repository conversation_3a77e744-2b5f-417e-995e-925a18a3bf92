#if UNITY_IOS && !UNITY_EDITOR
using System.Runtime.InteropServices;
#endif

namespace MUP.Core
{
    public class FeelManager : Singleton<FeelManager>
    {
#if UNITY_IOS && !UNITY_EDITOR
        [DllImport("__Internal")]
        private static extern void _TriggerImpactFeedback(int style);
        
        [DllImport("__Internal")]
        private static extern void _TriggerNotificationFeedback(int type);
        
        [DllImport("__Internal")]
        private static extern void _TriggerSelectionFeedback();
        
        [DllImport("__Internal")]
        private static extern bool _IsHapticsSupported();
#endif

#if UNITY_ANDROID && !UNITY_EDITOR
        private AndroidJavaObject vibrator;
        private AndroidJavaClass unityPlayer;
        private AndroidJavaObject currentActivity;
        private bool hasVibrationEffect = false;
        private int sdkVersion = 0;
#endif

        /// <summary>
        /// iOS Haptic Impact Feedback Types
        /// </summary>
        public enum HapticImpactStyle
        {
            Light = 0,
            Medium = 1,
            Heavy = 2,
            Rigid = 3,    // iOS 13+
            Soft = 4      // iOS 13+
        }

        /// <summary>
        /// iOS Haptic Notification Feedback Types
        /// </summary>
        public enum HapticNotificationType
        {
            Success = 0,
            Warning = 1,
            Error = 2
        }

        /// <summary>
        /// Android Vibration Patterns
        /// </summary>
        public enum VibrationPattern
        {
            Light,
            Medium,
            Heavy,
            Success,
            Warning,
            Error,
            Custom
        }

        public void Init()
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            InitializeAndroid();
#endif
        }

#if UNITY_ANDROID && !UNITY_EDITOR
        private void InitializeAndroid()
        {
            try
            {
                unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
                vibrator = currentActivity.Call<AndroidJavaObject>("getSystemService", "vibrator");
                
                // Check Android SDK version
                AndroidJavaClass versionClass = new AndroidJavaClass("android.os.Build$VERSION");
                sdkVersion = versionClass.GetStatic<int>("SDK_INT");
                hasVibrationEffect = sdkVersion >= 26; // Android API 26 (Android 8.0)
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[FeelManager] Failed to initialize Android vibration: {e.Message}");
                vibrator = null;
            }
        }
#endif

#if UNITY_IOS && !UNITY_EDITOR
        public void TriggerImpactFeedback(HapticImpactStyle style)
        {
            if (!IsHapticsSupported())
            {
                return;
            }

            try
            {
                _TriggerImpactFeedback((int)style);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[FeelManager] Failed to trigger iOS impact feedback: {e.Message}");
            }
        }

        public void TriggerNotificationFeedback(HapticNotificationType type)
        {
            if (!IsHapticsSupported())
            {
                return;
            }

            try
            {
                _TriggerNotificationFeedback((int)type);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[FeelManager] Failed to trigger iOS notification feedback: {e.Message}");
            }
        }

        public void TriggerSelectionFeedback()
        {
            if (!IsHapticsSupported())
            {
                return;
            }

            try
            {
                _TriggerSelectionFeedback();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[FeelManager] Failed to trigger iOS selection feedback: {e.Message}");
            }
        }

        public bool IsHapticsSupported()
        {
            try
            {
                return _IsHapticsSupported();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[FeelManager] Failed to check iOS haptics support: {e.Message}");
                return false;
            }
        }
#endif

#if UNITY_ANDROID && !UNITY_EDITOR
        public void TriggerVibration(VibrationPattern pattern)
        {
            if (vibrator == null)
            {
                return;
            }

            if (!HasVibrator())
            {
                return;
            }

            try
            {
                switch (pattern)
                {
                    case VibrationPattern.Light:
                        TriggerVibration(50);
                        break;
                    case VibrationPattern.Medium:
                        TriggerVibration(100);
                        break;
                    case VibrationPattern.Heavy:
                        TriggerVibration(200);
                        break;
                    case VibrationPattern.Success:
                        TriggerVibrationPattern(new long[] { 0, 100, 50, 100 });
                        break;
                    case VibrationPattern.Warning:
                        TriggerVibrationPattern(new long[] { 0, 150, 100, 150, 100, 150 });
                        break;
                    case VibrationPattern.Error:
                        TriggerVibrationPattern(new long[] { 0, 200, 100, 200, 100, 200 });
                        break;
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[FeelManager] Failed to trigger Android vibration: {e.Message}");
            }
        }

        public void TriggerVibration(long milliseconds)
        {
            if (vibrator == null || !HasVibrator()) return;

            try
            {
                if (hasVibrationEffect)
                {
                    // Use VibrationEffect for Android API 26+
                    AndroidJavaClass vibrationEffectClass = new AndroidJavaClass("android.os.VibrationEffect");
                    AndroidJavaObject vibrationEffect = vibrationEffectClass.CallStatic<AndroidJavaObject>(
                        "createOneShot", milliseconds, -1); // -1 = default amplitude
                    vibrator.Call("vibrate", vibrationEffect);
                }
                else
                {
                    // Use legacy vibrate method for older Android versions
                    vibrator.Call("vibrate", milliseconds);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[FeelManager] Failed to trigger Android vibration: {e.Message}");
            }
        }

        public void TriggerVibrationPattern(long[] pattern, int repeat = -1)
        {
            if (vibrator == null || !HasVibrator()) return;

            try
            {
                if (hasVibrationEffect)
                {
                    // Use VibrationEffect for Android API 26+
                    AndroidJavaClass vibrationEffectClass = new AndroidJavaClass("android.os.VibrationEffect");
                    AndroidJavaObject vibrationEffect = vibrationEffectClass.CallStatic<AndroidJavaObject>(
                        "createWaveform", pattern, repeat);
                    vibrator.Call("vibrate", vibrationEffect);
                }
                else
                {
                    // Use legacy vibrate method for older Android versions
                    vibrator.Call("vibrate", pattern, repeat);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[FeelManager] Failed to trigger Android vibration pattern: {e.Message}");
            }
        }

        public bool HasVibrator()
        {
            if (vibrator == null) return false;

            try
            {
                return vibrator.Call<bool>("hasVibrator");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[FeelManager] Failed to check Android vibrator capability: {e.Message}");
                return false;
            }
        }

        public void CancelVibration()
        {
            if (vibrator == null) return;

            try
            {
                vibrator.Call("cancel");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[FeelManager] Failed to cancel Android vibration: {e.Message}");
            }
        }
#endif

        public bool IsHapticFeedbackSupported()
        {
#if UNITY_IOS && !UNITY_EDITOR
            return IsHapticsSupported();
#elif UNITY_ANDROID && !UNITY_EDITOR
            return HasVibrator();
#else
            return false;
#endif
        }
    }
}
