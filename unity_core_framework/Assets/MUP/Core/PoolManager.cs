using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.Pool;
using UnityEngine.ResourceManagement.AsyncOperations;

namespace MUP.Core
{
    public enum EPoolType
    {
        SfxObject,
    }

    public interface IPoolableObject
    {
        void OnObjectCreate();
        void OnObjectGet();
        void OnObjectRelease();
        void OnObjectDestroy();
    }

    public class PoolManager : Singleton<PoolManager>
    {
        private Dictionary<EPoolType, IObjectPool<GameObject>> pools = new Dictionary<EPoolType, IObjectPool<GameObject>>();
        private PoolSO poolSO;

        public async UniTask Init()
        {
            AsyncOperationHandle<PoolSO> handle = Addressables.LoadAssetAsync<PoolSO>("Assets/MUP/SO/PoolSO.asset");
            poolSO = await handle.ToUniTask(cancellationToken: this.GetCancellationTokenOnDestroy());
        }

        public void InitPool(EPoolType type, int defaultCapacity, int maxSize)
        {
            GameObject prefab = poolSO.GetPool(type.ToString());

            IObjectPool<GameObject> pool = new ObjectPool<GameObject>(
                createFunc: () => CreatePoolObject(prefab),
                actionOnGet: OnGetFromPool,
                actionOnRelease: OnReleaseToPool,
                actionOnDestroy: OnDestroyPoolObject,
                collectionCheck: true,
                defaultCapacity: defaultCapacity,
                maxSize: maxSize
            );

            pools.Add(type, pool);
        }

        public GameObject Get(EPoolType type)
        {
            IObjectPool<GameObject> pool = GetPool(type);
            GameObject obj = pool.Get();
            return obj;
        }

        public void Release(EPoolType type, GameObject prefab)
        {
            if (pools.TryGetValue(type, out IObjectPool<GameObject> pool))
            {
                pool.Release(prefab);
            }
            else
            {
                Destroy(prefab);
            }
        }

        private IObjectPool<GameObject> GetPool(EPoolType type)
        {
            if (pools.TryGetValue(type, out IObjectPool<GameObject> pool))
            {
                return pool;
            }
            return null;
        }

        private GameObject CreatePoolObject(GameObject prefab)
        {
            GameObject obj = Instantiate(prefab);
            obj.transform.SetParent(transform);
            obj.GetComponent<IPoolableObject>().OnObjectCreate();
            return obj;
        }

        private void OnGetFromPool(GameObject obj)
        {
            obj.GetComponent<IPoolableObject>().OnObjectGet();
        }

        private void OnReleaseToPool(GameObject obj)
        {
            obj.GetComponent<IPoolableObject>().OnObjectRelease();
        }

        private void OnDestroyPoolObject(GameObject obj)
        {
            obj.GetComponent<IPoolableObject>().OnObjectDestroy();
        }
    }
}