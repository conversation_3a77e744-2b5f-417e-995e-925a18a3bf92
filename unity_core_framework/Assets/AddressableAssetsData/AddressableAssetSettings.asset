%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 468a46d0ae32c3544b7d98094e6448a9, type: 3}
  m_Name: AddressableAssetSettings
  m_EditorClassIdentifier: 
  m_DefaultGroup: 3414940625cec404299e7f4a604fa695
  m_currentHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_OptimizeCatalogSize: 0
  m_BuildRemoteCatalog: 0
  m_CatalogRequestsTimeout: 0
  m_DisableCatalogUpdateOnStart: 0
  m_InternalIdNamingMode: 0
  m_InternalBundleIdMode: 1
  m_AssetLoadMode: 0
  m_BundledAssetProviderType:
    m_AssemblyName: 
    m_ClassName: 
  m_AssetBundleProviderType:
    m_AssemblyName: 
    m_ClassName: 
  m_IgnoreUnsupportedFilesInBuild: 0
  m_UniqueBundleIds: 0
  m_EnableJsonCatalog: 0
  m_NonRecursiveBuilding: 1
  m_CCDEnabled: 0
  m_maxConcurrentWebRequests: 3
  m_UseUWRForLocalBundles: 0
  m_BundleTimeout: 0
  m_BundleRetryCount: 0
  m_BundleRedirectLimit: -1
  m_SharedBundleSettings: 0
  m_SharedBundleSettingsCustomGroupIndex: 0
  m_ContiguousBundles: 1
  m_StripUnityVersionFromBundleBuild: 0
  m_DisableVisibleSubAssetRepresentations: 0
  m_BuiltInBundleNaming: 0
  mBuiltInBundleCustomNaming: 
  m_MonoScriptBundleNaming: 0
  m_CheckForContentUpdateRestrictionsOption: 0
  m_MonoScriptBundleCustomNaming: 
  m_RemoteCatalogBuildPath:
    m_Id: 
  m_RemoteCatalogLoadPath:
    m_Id: 
  m_ContentStateBuildPathProfileVariableName: 
  m_CustomContentStateBuildPath: 
  m_ContentStateBuildPath: 
  m_BuildAddressablesWithPlayerBuild: 0
  m_overridePlayerVersion: '[UnityEditor.PlayerSettings.bundleVersion]'
  m_GroupAssets:
  - {fileID: 11400000, guid: cabdaff5f32304041ac48144b43c9fa1, type: 2}
  m_BuildSettings:
    m_LogResourceManagerExceptions: 1
    m_BundleBuildPath: Temp/com.unity.addressables/AssetBundles
  m_ProfileSettings:
    m_Profiles:
    - m_InheritedParent: 
      m_Id: 0ce4884ed5303400ab7fb40155e1b351
      m_ProfileName: Default
      m_Values:
      - m_Id: 14334472b5b46403d92e5e2eaf62880e
        m_Value: 'ServerData/[BuildTarget]'
      - m_Id: 240a38b0665e948fdac56f26dba6b978
        m_Value: <undefined>
      - m_Id: 26c1042f567a74277be03e0554108fc5
        m_Value: '[UnityEditor.EditorUserBuildSettings.activeBuildTarget]'
      - m_Id: 7195f157c340648bb80131716ea51545
        m_Value: '[UnityEngine.AddressableAssets.Addressables.BuildPath]/[BuildTarget]'
      - m_Id: e1fd2254e36974703a3cfa0381373bc4
        m_Value: '{UnityEngine.AddressableAssets.Addressables.RuntimePath}/[BuildTarget]'
    m_ProfileEntryNames:
    - m_Id: 14334472b5b46403d92e5e2eaf62880e
      m_Name: Remote.BuildPath
      m_InlineUsage: 0
    - m_Id: 240a38b0665e948fdac56f26dba6b978
      m_Name: Remote.LoadPath
      m_InlineUsage: 0
    - m_Id: 26c1042f567a74277be03e0554108fc5
      m_Name: BuildTarget
      m_InlineUsage: 0
    - m_Id: 7195f157c340648bb80131716ea51545
      m_Name: Local.BuildPath
      m_InlineUsage: 0
    - m_Id: e1fd2254e36974703a3cfa0381373bc4
      m_Name: Local.LoadPath
      m_InlineUsage: 0
    m_ProfileVersion: 1
  m_LabelTable:
    m_LabelNames:
    - default
  m_SchemaTemplates: []
  m_GroupTemplateObjects:
  - {fileID: 11400000, guid: 4917099f32beb4baaa565301a7375c8d, type: 2}
  m_InitializationObjects: []
  m_CertificateHandlerType:
    m_AssemblyName: 
    m_ClassName: 
  m_ActivePlayerDataBuilderIndex: 2
  m_DataBuilders:
  - {fileID: 11400000, guid: 40d1cf002275c4fc78a3064be199e263, type: 2}
  - {fileID: 11400000, guid: ed65b421378cd4f8d8c8d33f6abd1e3c, type: 2}
  - {fileID: 11400000, guid: ca60bf59e19c74f8989ab09bee9dd2fe, type: 2}
  m_ActiveProfileId: 0ce4884ed5303400ab7fb40155e1b351
