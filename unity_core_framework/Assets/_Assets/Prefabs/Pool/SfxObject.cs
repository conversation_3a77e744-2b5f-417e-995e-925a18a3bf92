using Cysharp.Threading.Tasks;
using MUP.Core;
using MUP.Utils;
using UnityEngine;

namespace MUP.Audio
{
    public class SfxObject : MonoBehaviour, IPoolableObject
    {
        public AudioSource audioSource;

        public void OnObjectCreate()
        {

        }

        public void OnObjectGet()
        {
            gameObject.SetActive(true);
        }

        public void OnObjectRelease()
        {
            gameObject.SetActive(false);
        }

        public void OnObjectDestroy()
        {
            Destroy(gameObject);
        }

        public async UniTaskVoid PlaySfx(AudioClip audioClip)
        {
            audioSource.clip = audioClip;
            audioSource.Play();

            while (audioSource.isPlaying)
            {
                await UniTask.Delay(Constant.MILLISECOND_1000, cancellationToken: this.GetCancellationTokenOnDestroy());
            }

            PoolManager.Instance.Release(EPoolType.SfxObject, gameObject);
        }
    }
}