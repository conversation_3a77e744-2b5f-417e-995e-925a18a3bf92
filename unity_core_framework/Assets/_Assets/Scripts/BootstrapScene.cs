using MUP.Core;
using MUP.Audio;
using UnityEngine;
using UnityEngine.SceneManagement;

public class BoosterManager : MonoBehaviour
{
    async void Start()
    {
        var booster = new Booster();

        booster.AddRequiredTask(async () => await PoolManager.Instance.Init());

        booster.AddDependentTask(async () => await AudioManager.Instance.Init());

        await booster.ExecuteAsync();

        await SceneManager.LoadSceneAsync("MainScene");
    }
}
