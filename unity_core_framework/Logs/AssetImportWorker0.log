Unity Editor version:    6000.0.51f1 (01c3ff5872c5)
Branch:                  6000.0/respin/6000.0.51f1-a206c6c19c75
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.5 (Build 24F74)
Darwin version:          24.5.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        16384 MB
Using pre-set license

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/Desktop/Projects/unity_core_framework/unity_core_framework
-logFile
Logs/AssetImportWorker0.log
-srvPort
54011
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/Desktop/Projects/unity_core_framework/unity_core_framework
/Users/<USER>/Desktop/Projects/unity_core_framework/unity_core_framework
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8421891840]  Target information:

Player connection [8421891840]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2896821326 [EditorId] 2896821326 [Version] 1048832 [Id] OSXEditor(0,************) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8421891840]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2896821326 [EditorId] 2896821326 [Version] 1048832 [Id] OSXEditor(0,************) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8421891840] Host joined multi-casting on [***********:54997]...
Player connection [8421891840] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.51f1 (01c3ff5872c5)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Desktop/Projects/unity_core_framework/unity_core_framework/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56140
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.51f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.51f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.004116 seconds.
- Loaded All Assemblies, in  0.339 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.395 seconds
Domain Reload Profiling: 735ms
	BeginReloadAssembly (116ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (139ms)
		LoadAssemblies (117ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (135ms)
			TypeCache.Refresh (134ms)
				TypeCache.ScanAssembly (122ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (396ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (356ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (140ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (54ms)
			ProcessInitializeOnLoadAttributes (96ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.758 seconds
Refreshing native plugins compatible for Editor in 0.50 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.718 seconds
Domain Reload Profiling: 1471ms
	BeginReloadAssembly (111ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (572ms)
		LoadAssemblies (305ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (310ms)
			TypeCache.Refresh (252ms)
				TypeCache.ScanAssembly (230ms)
			BuildScriptInfoCaches (46ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (718ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (556ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (141ms)
			ProcessInitializeOnLoadAttributes (291ms)
			ProcessInitializeOnLoadMethodAttributes (103ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launching external process: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.06 seconds
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 213 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6572 unused Assets / (3.0 MB). Loaded Objects now: 7277.
Memory consumption went from 176.4 MB to 173.4 MB.
Total: 67.353416 ms (FindLiveObjects: 9.472500 ms CreateObjectMapping: 0.581583 ms MarkObjects: 51.174792 ms  DeleteObjects: 6.123500 ms)

========================================================================
Received Import Request.
  Time since last request: 1338463.987709 seconds.
  path: Assets/InputSystem_Actions.inputactions
  artifactKey: Guid(2bcd2660ca9b64942af0de543d8d7100) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/InputSystem_Actions.inputactions using Guid(2bcd2660ca9b64942af0de543d8d7100) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1526cf67c1d1aaa9a72597cdbc7bb0b0') in 0.055466667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000184 seconds.
  path: Assets/Scenes
  artifactKey: Guid(109e9bdd0fcc649c5bc59085c35bdc83) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes using Guid(109e9bdd0fcc649c5bc59085c35bdc83) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'afdff4c3a2fdaa6f8b2ca806ac4aa93d') in 0.000587334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(93b439a37f63240aca3dd4e01d978a9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniversalRenderPipelineGlobalSettings.asset using Guid(93b439a37f63240aca3dd4e01d978a9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '55ddc091ea4b5bd2af7aba02c481c44f') in 0.000947458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 14 unused Assets / (1.8 MB). Loaded Objects now: 7280.
Memory consumption went from 137.2 MB to 135.4 MB.
Total: 26.254208 ms (FindLiveObjects: 0.293708 ms CreateObjectMapping: 0.123709 ms MarkObjects: 25.557167 ms  DeleteObjects: 0.279041 ms)

Prepare: number of updated asset objects reloaded= 25
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 110 unused Assets / (13.2 MB). Loaded Objects now: 7160.
Memory consumption went from 135.2 MB to 122.0 MB.
Total: 6.635000 ms (FindLiveObjects: 0.233084 ms CreateObjectMapping: 0.106500 ms MarkObjects: 4.386500 ms  DeleteObjects: 1.908500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 25.676046 seconds.
  path: Assets
  artifactKey: Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets using Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c391582d739b6e62a5220f35cbb167ac') in 0.000570834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x335887000 may have been prematurely finalized
- Loaded All Assemblies, in  0.824 seconds
Refreshing native plugins compatible for Editor in 0.48 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.733 seconds
Domain Reload Profiling: 1560ms
	BeginReloadAssembly (297ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (176ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (464ms)
		LoadAssemblies (257ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (254ms)
			TypeCache.Refresh (115ms)
				TypeCache.ScanAssembly (89ms)
			BuildScriptInfoCaches (124ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (733ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (596ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (142ms)
			ProcessInitializeOnLoadAttributes (390ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6589 unused Assets / (5.8 MB). Loaded Objects now: 7163.
Memory consumption went from 135.1 MB to 129.3 MB.
Total: 7.094459 ms (FindLiveObjects: 0.471541 ms CreateObjectMapping: 0.283084 ms MarkObjects: 4.306083 ms  DeleteObjects: 2.033625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x326147000 may have been prematurely finalized
- Loaded All Assemblies, in  0.975 seconds
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.549 seconds
Domain Reload Profiling: 1534ms
	BeginReloadAssembly (219ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (25ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (708ms)
		LoadAssemblies (443ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (312ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (264ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (549ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (432ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (120ms)
			ProcessInitializeOnLoadAttributes (251ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6239 unused Assets / (5.8 MB). Loaded Objects now: 6815.
Memory consumption went from 134.9 MB to 129.1 MB.
Total: 6.619375 ms (FindLiveObjects: 0.354500 ms CreateObjectMapping: 0.224750 ms MarkObjects: 4.120834 ms  DeleteObjects: 1.919084 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x326787000 may have been prematurely finalized
- Loaded All Assemblies, in  0.786 seconds
Refreshing native plugins compatible for Editor in 0.52 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.544 seconds
Domain Reload Profiling: 1332ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (531ms)
		LoadAssemblies (389ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (175ms)
			TypeCache.Refresh (88ms)
				TypeCache.ScanAssembly (71ms)
			BuildScriptInfoCaches (73ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (544ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (392ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (218ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4652 unused Assets / (4.8 MB). Loaded Objects now: 5236.
Memory consumption went from 129.8 MB to 125.0 MB.
Total: 5.945292 ms (FindLiveObjects: 0.313250 ms CreateObjectMapping: 0.147750 ms MarkObjects: 3.993334 ms  DeleteObjects: 1.490584 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f983000 may have been prematurely finalized
- Loaded All Assemblies, in  0.683 seconds
Refreshing native plugins compatible for Editor in 3.56 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.561 seconds
Domain Reload Profiling: 1246ms
	BeginReloadAssembly (185ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (403ms)
		LoadAssemblies (303ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (150ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (121ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (561ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (408ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (236ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4609 unused Assets / (4.7 MB). Loaded Objects now: 5196.
Memory consumption went from 129.3 MB to 124.7 MB.
Total: 5.978542 ms (FindLiveObjects: 0.261708 ms CreateObjectMapping: 0.124083 ms MarkObjects: 4.143792 ms  DeleteObjects: 1.448542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e687000 may have been prematurely finalized
- Loaded All Assemblies, in  0.656 seconds
Refreshing native plugins compatible for Editor in 0.34 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.463 seconds
Domain Reload Profiling: 1122ms
	BeginReloadAssembly (187ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (395ms)
		LoadAssemblies (248ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (464ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (359ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (206ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4547 unused Assets / (4.6 MB). Loaded Objects now: 5136.
Memory consumption went from 129.6 MB to 125.1 MB.
Total: 6.089458 ms (FindLiveObjects: 0.330500 ms CreateObjectMapping: 0.139333 ms MarkObjects: 4.046875 ms  DeleteObjects: 1.572333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.19 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5352 unused Assets / (3.9 MB). Loaded Objects now: 5949.
Memory consumption went from 123.7 MB to 119.7 MB.
Total: 23.025459 ms (FindLiveObjects: 0.472000 ms CreateObjectMapping: 0.238750 ms MarkObjects: 19.252917 ms  DeleteObjects: 3.060584 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.47 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5352 unused Assets / (3.5 MB). Loaded Objects now: 5949.
Memory consumption went from 121.4 MB to 117.9 MB.
Total: 91.589125 ms (FindLiveObjects: 0.636792 ms CreateObjectMapping: 0.227416 ms MarkObjects: 87.406584 ms  DeleteObjects: 3.317292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e687000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.652 seconds
Refreshing native plugins compatible for Editor in 0.81 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.739 seconds
Domain Reload Profiling: 1393ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (394ms)
		LoadAssemblies (248ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (129ms)
				TypeCache.ScanAssembly (113ms)
			BuildScriptInfoCaches (52ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (739ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (565ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (53ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (201ms)
			ProcessInitializeOnLoadAttributes (254ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.41 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5305 unused Assets / (4.6 MB). Loaded Objects now: 5901.
Memory consumption went from 132.5 MB to 127.9 MB.
Total: 7.604584 ms (FindLiveObjects: 0.373708 ms CreateObjectMapping: 0.198083 ms MarkObjects: 4.705500 ms  DeleteObjects: 2.326792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.33 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5297 unused Assets / (4.2 MB). Loaded Objects now: 5901.
Memory consumption went from 122.8 MB to 118.6 MB.
Total: 7.267084 ms (FindLiveObjects: 0.403459 ms CreateObjectMapping: 0.224167 ms MarkObjects: 4.862750 ms  DeleteObjects: 1.775667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.41 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5303 unused Assets / (4.2 MB). Loaded Objects now: 5901.
Memory consumption went from 122.6 MB to 118.4 MB.
Total: 14.940750 ms (FindLiveObjects: 0.429000 ms CreateObjectMapping: 0.175084 ms MarkObjects: 12.374958 ms  DeleteObjects: 1.961000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.36 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5303 unused Assets / (4.3 MB). Loaded Objects now: 5901.
Memory consumption went from 122.5 MB to 118.2 MB.
Total: 6.766667 ms (FindLiveObjects: 0.367333 ms CreateObjectMapping: 0.164209 ms MarkObjects: 4.466500 ms  DeleteObjects: 1.768209 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e687000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.929 seconds
Refreshing native plugins compatible for Editor in 0.32 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.516 seconds
Domain Reload Profiling: 1448ms
	BeginReloadAssembly (248ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (96ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (622ms)
		LoadAssemblies (357ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (332ms)
			TypeCache.Refresh (225ms)
				TypeCache.ScanAssembly (205ms)
			BuildScriptInfoCaches (97ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (517ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (392ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (190ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.41 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5311 unused Assets / (3.5 MB). Loaded Objects now: 5906.
Memory consumption went from 129.0 MB to 125.6 MB.
Total: 9.763292 ms (FindLiveObjects: 0.365333 ms CreateObjectMapping: 0.517792 ms MarkObjects: 7.040083 ms  DeleteObjects: 1.839500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e687000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.822 seconds
Refreshing native plugins compatible for Editor in 0.34 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.467 seconds
Domain Reload Profiling: 1291ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (573ms)
		LoadAssemblies (357ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (256ms)
			TypeCache.Refresh (42ms)
				TypeCache.ScanAssembly (10ms)
			BuildScriptInfoCaches (204ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (467ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (354ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (167ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.37 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5366 unused Assets / (5.2 MB). Loaded Objects now: 5966.
Memory consumption went from 129.4 MB to 124.2 MB.
Total: 6.869416 ms (FindLiveObjects: 0.348959 ms CreateObjectMapping: 0.177709 ms MarkObjects: 4.670167 ms  DeleteObjects: 1.672250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e687000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.880 seconds
Refreshing native plugins compatible for Editor in 0.34 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.475 seconds
Domain Reload Profiling: 1357ms
	BeginReloadAssembly (187ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (639ms)
		LoadAssemblies (377ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (303ms)
			TypeCache.Refresh (149ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (145ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (475ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (358ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (171ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.39 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5366 unused Assets / (5.0 MB). Loaded Objects now: 5971.
Memory consumption went from 129.6 MB to 124.6 MB.
Total: 6.974292 ms (FindLiveObjects: 0.374667 ms CreateObjectMapping: 0.207500 ms MarkObjects: 4.699333 ms  DeleteObjects: 1.692083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 677.508895 seconds.
  path: Assets/MUP
  artifactKey: Guid(9cb4fe961de434746bb754f0a251d6be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MUP using Guid(9cb4fe961de434746bb754f0a251d6be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'de89ef36c47d971923ebd874ace4aa52') in 0.007357959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 7.665467 seconds.
  path: Assets/NuGet.config
  artifactKey: Guid(e3da297c6556f42cc99bc5726f473f61) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/NuGet.config using Guid(e3da297c6556f42cc99bc5726f473f61) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Could not generate preview image
 -> (artifact id: 'e8c7cfbc256fe34cf6bd7fae30268a75') in 0.004960708 seconds
  ERROR: Import of 'Assets/NuGet.config' had errors: Could not generate preview image
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 5.222261 seconds.
  path: Assets/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(93b439a37f63240aca3dd4e01d978a9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniversalRenderPipelineGlobalSettings.asset using Guid(93b439a37f63240aca3dd4e01d978a9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c05fe6938422348003909ce6a48ab628') in 0.120508292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 96

========================================================================
Received Import Request.
  Time since last request: 0.725519 seconds.
  path: Assets/packages.config
  artifactKey: Guid(cb97f3526f8614c1cb0fe14186f7abdd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/packages.config using Guid(cb97f3526f8614c1cb0fe14186f7abdd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Could not generate preview image
 -> (artifact id: 'e6efa666f7d879bf6a411c749cb412c7') in 0.002484958 seconds
  ERROR: Import of 'Assets/packages.config' had errors: Could not generate preview image
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 7.785945 seconds.
  path: Assets/DefaultVolumeProfile.asset
  artifactKey: Guid(3f9215ea0144899419cfbc0957140d3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/DefaultVolumeProfile.asset using Guid(3f9215ea0144899419cfbc0957140d3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '93d540106fa17522582dfb922835bf19') in 0.001164333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 23.068200 seconds.
  path: Assets/_Assets
  artifactKey: Guid(6dd734c9def124f93b91f651033d4abf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Assets using Guid(6dd734c9def124f93b91f651033d4abf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db9f3422d9296404b2513b73b7fa7d5a') in 0.001898208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 8.195292 seconds.
  path: Assets/_Assets/Scenes
  artifactKey: Guid(5e8efca6680c442caabf36e7970e211f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Assets/Scenes using Guid(5e8efca6680c442caabf36e7970e211f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '94e8b7b17c6fbf23a7a094e142ef9131') in 0.000627291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 9.253664 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SampleScene.unity using Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '90350951bde77d39f02da94b89ef04f8') in 0.001149041 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 9.726375 seconds.
  path: Assets/Packages
  artifactKey: Guid(793203f47492e4300963feedd1643444) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Packages using Guid(793203f47492e4300963feedd1643444) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd4f1a912f4b31c57b469a880fd435700') in 0.000552625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 34.666714 seconds.
  path: Assets/_Assets/Scenes/BootstrapScene.unity
  artifactKey: Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Assets/Scenes/BootstrapScene.unity using Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a9a19b2d2b88269d1490f2dfabad1221') in 0.021935125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 33.672205 seconds.
  path: Assets/MUP/Core
  artifactKey: Guid(3be5b8d6cfa8c4a3a81db070a4267443) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MUP/Core using Guid(3be5b8d6cfa8c4a3a81db070a4267443) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa96486e8f80144ff055684f757c7d87') in 0.003892084 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.89 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5359 unused Assets / (3.9 MB). Loaded Objects now: 5972.
Memory consumption went from 123.4 MB to 119.5 MB.
Total: 17.404375 ms (FindLiveObjects: 0.474458 ms CreateObjectMapping: 0.212166 ms MarkObjects: 14.803292 ms  DeleteObjects: 1.913209 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 50.424186 seconds.
  path: Assets/MUP/Core/MUP_Singleton.cs
  artifactKey: Guid(e6bff96f0952c4ae6892feb4cf5741f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MUP/Core/MUP_Singleton.cs using Guid(e6bff96f0952c4ae6892feb4cf5741f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'de4afd07f8b38a59a7d9f6739789b30b') in 0.001071208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17e687000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.773 seconds
Refreshing native plugins compatible for Editor in 0.27 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.556 seconds
Domain Reload Profiling: 1330ms
	BeginReloadAssembly (219ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (500ms)
		LoadAssemblies (338ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (195ms)
			TypeCache.Refresh (46ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (139ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (556ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (443ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (252ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.36 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5367 unused Assets / (4.8 MB). Loaded Objects now: 5977.
Memory consumption went from 129.2 MB to 124.4 MB.
Total: 6.696583 ms (FindLiveObjects: 0.287541 ms CreateObjectMapping: 0.168209 ms MarkObjects: 4.585333 ms  DeleteObjects: 1.654750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17df0b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.035 seconds
Refreshing native plugins compatible for Editor in 0.39 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.480 seconds
Domain Reload Profiling: 1519ms
	BeginReloadAssembly (310ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (164ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (601ms)
		LoadAssemblies (381ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (267ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (239ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (481ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (362ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (120ms)
			ProcessInitializeOnLoadAttributes (164ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.41 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5367 unused Assets / (3.3 MB). Loaded Objects now: 5982.
Memory consumption went from 129.2 MB to 125.9 MB.
Total: 8.453625 ms (FindLiveObjects: 0.522541 ms CreateObjectMapping: 0.440084 ms MarkObjects: 5.744292 ms  DeleteObjects: 1.746042 ms)

Prepare: number of updated asset objects reloaded= 0
[usbmuxd] Attached: 37 00008030-0001658A34F9402E
[usbmuxd] Detached: 37 00008030-0001658A34F9402E
[usbmuxd] Attached: 38 00008030-0001658A34F9402E
