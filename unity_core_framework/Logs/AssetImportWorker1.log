Unity Editor version:    6000.0.51f1 (01c3ff5872c5)
Branch:                  6000.0/respin/6000.0.51f1-a206c6c19c75
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.5 (Build 24F74)
Darwin version:          24.5.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        16384 MB
Using pre-set license

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/Desktop/Projects/unity_core_framework/unity_core_framework
-logFile
Logs/AssetImportWorker1.log
-srvPort
54011
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/Desktop/Projects/unity_core_framework/unity_core_framework
/Users/<USER>/Desktop/Projects/unity_core_framework/unity_core_framework
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8421891840]  Target information:

Player connection [8421891840]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3949801430 [EditorId] 3949801430 [Version] 1048832 [Id] OSXEditor(0,************) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8421891840]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3949801430 [EditorId] 3949801430 [Version] 1048832 [Id] OSXEditor(0,************) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8421891840] Host joined multi-casting on [***********:54997]...
Player connection [8421891840] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.51f1 (01c3ff5872c5)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Desktop/Projects/unity_core_framework/unity_core_framework/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56142
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.51f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.51f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.003829 seconds.
- Loaded All Assemblies, in  0.340 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.391 seconds
Domain Reload Profiling: 731ms
	BeginReloadAssembly (116ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (140ms)
		LoadAssemblies (117ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (136ms)
			TypeCache.Refresh (135ms)
				TypeCache.ScanAssembly (122ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (391ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (353ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (142ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (53ms)
			ProcessInitializeOnLoadAttributes (90ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.750 seconds
Refreshing native plugins compatible for Editor in 0.51 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.711 seconds
Domain Reload Profiling: 1461ms
	BeginReloadAssembly (117ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (565ms)
		LoadAssemblies (299ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (311ms)
			TypeCache.Refresh (253ms)
				TypeCache.ScanAssembly (230ms)
			BuildScriptInfoCaches (46ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (712ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (558ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (139ms)
			ProcessInitializeOnLoadAttributes (292ms)
			ProcessInitializeOnLoadMethodAttributes (103ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launching external process: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.06 seconds
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 213 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6572 unused Assets / (3.8 MB). Loaded Objects now: 7277.
Memory consumption went from 176.6 MB to 172.8 MB.
Total: 68.162875 ms (FindLiveObjects: 9.450042 ms CreateObjectMapping: 0.564375 ms MarkObjects: 51.923333 ms  DeleteObjects: 6.224500 ms)

========================================================================
Received Import Request.
  Time since last request: 1338463.991354 seconds.
  path: Assets/Settings/UniversalRP.asset
  artifactKey: Guid(681886c5eb7344803b6206f758bf0b1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/UniversalRP.asset using Guid(681886c5eb7344803b6206f758bf0b1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '910a3316ce45b604f98c2ad003aa3cfb') in 0.047927417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000098 seconds.
  path: Assets/Settings
  artifactKey: Guid(0220aab0833d04faeb927d84ca6cc40c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings using Guid(0220aab0833d04faeb927d84ca6cc40c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '62508b3dd32630cf358da27d8a59d33e') in 0.000587333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/DefaultVolumeProfile.asset
  artifactKey: Guid(3f9215ea0144899419cfbc0957140d3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/DefaultVolumeProfile.asset using Guid(3f9215ea0144899419cfbc0957140d3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1e3ee85287093c2e01e2184a29fd3edf') in 0.001122916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 14 unused Assets / (1.8 MB). Loaded Objects now: 7280.
Memory consumption went from 137.4 MB to 135.6 MB.
Total: 25.924583 ms (FindLiveObjects: 0.299875 ms CreateObjectMapping: 0.106292 ms MarkObjects: 25.146333 ms  DeleteObjects: 0.371709 ms)

Prepare: number of updated asset objects reloaded= 25
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 110 unused Assets / (13.2 MB). Loaded Objects now: 7160.
Memory consumption went from 135.4 MB to 122.2 MB.
Total: 6.570583 ms (FindLiveObjects: 0.233417 ms CreateObjectMapping: 0.110250 ms MarkObjects: 4.395708 ms  DeleteObjects: 1.830667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x33d087000 may have been prematurely finalized
- Loaded All Assemblies, in  0.822 seconds
Refreshing native plugins compatible for Editor in 0.42 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.721 seconds
Domain Reload Profiling: 1546ms
	BeginReloadAssembly (300ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (159ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (464ms)
		LoadAssemblies (259ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (258ms)
			TypeCache.Refresh (121ms)
				TypeCache.ScanAssembly (91ms)
			BuildScriptInfoCaches (125ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (721ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (588ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (142ms)
			ProcessInitializeOnLoadAttributes (378ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6589 unused Assets / (5.8 MB). Loaded Objects now: 7163.
Memory consumption went from 139.7 MB to 134.0 MB.
Total: 7.031833 ms (FindLiveObjects: 0.548042 ms CreateObjectMapping: 0.226583 ms MarkObjects: 4.259000 ms  DeleteObjects: 1.997541 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f887000 may have been prematurely finalized
- Loaded All Assemblies, in  0.972 seconds
Refreshing native plugins compatible for Editor in 0.56 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.537 seconds
Domain Reload Profiling: 1516ms
	BeginReloadAssembly (211ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (711ms)
		LoadAssemblies (442ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (317ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (272ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (538ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (423ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (115ms)
			ProcessInitializeOnLoadAttributes (250ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6239 unused Assets / (5.3 MB). Loaded Objects now: 6815.
Memory consumption went from 139.6 MB to 134.2 MB.
Total: 6.429083 ms (FindLiveObjects: 0.350541 ms CreateObjectMapping: 0.235750 ms MarkObjects: 4.084417 ms  DeleteObjects: 1.757625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fa8b000 may have been prematurely finalized
- Loaded All Assemblies, in  0.786 seconds
Refreshing native plugins compatible for Editor in 0.48 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.527 seconds
Domain Reload Profiling: 1316ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (530ms)
		LoadAssemblies (387ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (174ms)
			TypeCache.Refresh (87ms)
				TypeCache.ScanAssembly (67ms)
			BuildScriptInfoCaches (72ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (527ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (393ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (116ms)
			ProcessInitializeOnLoadAttributes (220ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4652 unused Assets / (4.7 MB). Loaded Objects now: 5236.
Memory consumption went from 134.6 MB to 129.9 MB.
Total: 5.883625 ms (FindLiveObjects: 0.314708 ms CreateObjectMapping: 0.147458 ms MarkObjects: 3.998167 ms  DeleteObjects: 1.422375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f913000 may have been prematurely finalized
- Loaded All Assemblies, in  0.666 seconds
Refreshing native plugins compatible for Editor in 2.44 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.562 seconds
Domain Reload Profiling: 1230ms
	BeginReloadAssembly (179ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (405ms)
		LoadAssemblies (303ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (152ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (123ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (562ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (407ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (236ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4609 unused Assets / (4.6 MB). Loaded Objects now: 5196.
Memory consumption went from 134.2 MB to 129.6 MB.
Total: 5.917208 ms (FindLiveObjects: 0.263458 ms CreateObjectMapping: 0.126542 ms MarkObjects: 4.129417 ms  DeleteObjects: 1.397375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f99f000 may have been prematurely finalized
- Loaded All Assemblies, in  0.633 seconds
Refreshing native plugins compatible for Editor in 0.34 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.468 seconds
Domain Reload Profiling: 1103ms
	BeginReloadAssembly (187ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (370ms)
		LoadAssemblies (245ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (163ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (134ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (468ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (362ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (208ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4547 unused Assets / (4.6 MB). Loaded Objects now: 5136.
Memory consumption went from 134.5 MB to 129.9 MB.
Total: 5.872875 ms (FindLiveObjects: 0.313625 ms CreateObjectMapping: 0.143458 ms MarkObjects: 3.987500 ms  DeleteObjects: 1.427625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.26 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5352 unused Assets / (4.1 MB). Loaded Objects now: 5949.
Memory consumption went from 128.5 MB to 124.4 MB.
Total: 21.698834 ms (FindLiveObjects: 0.467458 ms CreateObjectMapping: 0.235000 ms MarkObjects: 19.280875 ms  DeleteObjects: 1.715125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.41 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5352 unused Assets / (3.1 MB). Loaded Objects now: 5949.
Memory consumption went from 126.2 MB to 123.0 MB.
Total: 83.728625 ms (FindLiveObjects: 0.542959 ms CreateObjectMapping: 0.235000 ms MarkObjects: 81.158500 ms  DeleteObjects: 1.791417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f99f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.660 seconds
Refreshing native plugins compatible for Editor in 3.27 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.723 seconds
Domain Reload Profiling: 1384ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (409ms)
		LoadAssemblies (252ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (199ms)
			TypeCache.Refresh (143ms)
				TypeCache.ScanAssembly (125ms)
			BuildScriptInfoCaches (47ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (723ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (537ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (152ms)
			ProcessInitializeOnLoadAttributes (300ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.43 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5305 unused Assets / (4.0 MB). Loaded Objects now: 5901.
Memory consumption went from 132.8 MB to 128.8 MB.
Total: 7.131333 ms (FindLiveObjects: 0.341584 ms CreateObjectMapping: 0.194208 ms MarkObjects: 4.672375 ms  DeleteObjects: 1.922250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.38 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5297 unused Assets / (3.5 MB). Loaded Objects now: 5901.
Memory consumption went from 123.2 MB to 119.7 MB.
Total: 7.105042 ms (FindLiveObjects: 0.389916 ms CreateObjectMapping: 0.218709 ms MarkObjects: 4.915416 ms  DeleteObjects: 1.580041 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.40 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5303 unused Assets / (5.0 MB). Loaded Objects now: 5901.
Memory consumption went from 122.5 MB to 117.5 MB.
Total: 14.904334 ms (FindLiveObjects: 0.422417 ms CreateObjectMapping: 0.198125 ms MarkObjects: 12.446083 ms  DeleteObjects: 1.836542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.37 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5303 unused Assets / (4.4 MB). Loaded Objects now: 5901.
Memory consumption went from 122.5 MB to 118.2 MB.
Total: 6.663417 ms (FindLiveObjects: 0.363667 ms CreateObjectMapping: 0.164917 ms MarkObjects: 4.444083 ms  DeleteObjects: 1.690292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f99f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.908 seconds
Refreshing native plugins compatible for Editor in 0.33 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.524 seconds
Domain Reload Profiling: 1435ms
	BeginReloadAssembly (239ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (613ms)
		LoadAssemblies (336ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (321ms)
			TypeCache.Refresh (215ms)
				TypeCache.ScanAssembly (182ms)
			BuildScriptInfoCaches (95ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (524ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (403ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (147ms)
			ProcessInitializeOnLoadAttributes (183ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.45 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5311 unused Assets / (4.0 MB). Loaded Objects now: 5906.
Memory consumption went from 129.0 MB to 125.0 MB.
Total: 8.360875 ms (FindLiveObjects: 0.363500 ms CreateObjectMapping: 0.161083 ms MarkObjects: 5.737083 ms  DeleteObjects: 2.098042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f99f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.833 seconds
Refreshing native plugins compatible for Editor in 0.33 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.460 seconds
Domain Reload Profiling: 1295ms
	BeginReloadAssembly (192ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (579ms)
		LoadAssemblies (360ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (263ms)
			TypeCache.Refresh (47ms)
				TypeCache.ScanAssembly (12ms)
			BuildScriptInfoCaches (206ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (461ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (349ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (165ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.38 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5366 unused Assets / (5.0 MB). Loaded Objects now: 5966.
Memory consumption went from 129.5 MB to 124.5 MB.
Total: 6.709542 ms (FindLiveObjects: 0.349459 ms CreateObjectMapping: 0.191416 ms MarkObjects: 4.592167 ms  DeleteObjects: 1.575584 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f99f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.889 seconds
Refreshing native plugins compatible for Editor in 0.33 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.476 seconds
Domain Reload Profiling: 1367ms
	BeginReloadAssembly (193ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (638ms)
		LoadAssemblies (364ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (307ms)
			TypeCache.Refresh (155ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (477ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (359ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (115ms)
			ProcessInitializeOnLoadAttributes (171ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.41 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5366 unused Assets / (4.8 MB). Loaded Objects now: 5971.
Memory consumption went from 129.8 MB to 124.9 MB.
Total: 6.896042 ms (FindLiveObjects: 0.372750 ms CreateObjectMapping: 0.214708 ms MarkObjects: 4.745375 ms  DeleteObjects: 1.562500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.90 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5359 unused Assets / (3.9 MB). Loaded Objects now: 5972.
Memory consumption went from 123.6 MB to 119.7 MB.
Total: 16.621834 ms (FindLiveObjects: 0.460584 ms CreateObjectMapping: 0.178792 ms MarkObjects: 14.171708 ms  DeleteObjects: 1.809167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f99f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.768 seconds
Refreshing native plugins compatible for Editor in 0.32 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.546 seconds
Domain Reload Profiling: 1315ms
	BeginReloadAssembly (217ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (496ms)
		LoadAssemblies (338ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (197ms)
			TypeCache.Refresh (48ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (139ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (546ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (433ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (245ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.42 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5367 unused Assets / (4.6 MB). Loaded Objects now: 5977.
Memory consumption went from 129.3 MB to 124.7 MB.
Total: 6.133458 ms (FindLiveObjects: 0.298417 ms CreateObjectMapping: 0.154542 ms MarkObjects: 4.130958 ms  DeleteObjects: 1.549167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 900.362663 seconds.
  path: Assets/MUP/Core/MUP_Singleton.cs
  artifactKey: Guid(e6bff96f0952c4ae6892feb4cf5741f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MUP/Core/MUP_Singleton.cs using Guid(e6bff96f0952c4ae6892feb4cf5741f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b13d4830a6d426b7f9220551a572ddc') in 0.005869917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17f99f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.091 seconds
Refreshing native plugins compatible for Editor in 0.32 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.477 seconds
Domain Reload Profiling: 1571ms
	BeginReloadAssembly (312ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (165ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (663ms)
		LoadAssemblies (390ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (318ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (271ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (477ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (358ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (120ms)
			ProcessInitializeOnLoadAttributes (163ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.34 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5367 unused Assets / (3.3 MB). Loaded Objects now: 5982.
Memory consumption went from 129.4 MB to 126.1 MB.
Total: 8.299583 ms (FindLiveObjects: 0.425916 ms CreateObjectMapping: 0.242500 ms MarkObjects: 5.484959 ms  DeleteObjects: 2.144583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 211.504438 seconds.
  path: Assets/MUP/Core/MUP_Singleton.cs
  artifactKey: Guid(e6bff96f0952c4ae6892feb4cf5741f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MUP/Core/MUP_Singleton.cs using Guid(e6bff96f0952c4ae6892feb4cf5741f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '78b0076655e3b1d94a2db84fe85dbb66') in 0.003394125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

[usbmuxd] Attached: 37 00008030-0001658A34F9402E
[usbmuxd] Detached: 37 00008030-0001658A34F9402E
[usbmuxd] Attached: 38 00008030-0001658A34F9402E
